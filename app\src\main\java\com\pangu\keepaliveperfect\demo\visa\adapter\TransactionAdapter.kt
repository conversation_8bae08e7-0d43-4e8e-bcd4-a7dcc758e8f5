package com.pangu.keepaliveperfect.demo.visa.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.visa.model.Transaction
import java.text.NumberFormat
import java.util.*

/**
 * 交易记录适配器
 */
class TransactionAdapter(private val transactions: List<Transaction>) : 
    RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_transaction, parent, false)
        return TransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(transactions[position])
    }

    override fun getItemCount(): Int = transactions.size

    inner class TransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvMerchantName: TextView = itemView.findViewById(R.id.tvMerchantName)
        private val tvTransactionTime: TextView = itemView.findViewById(R.id.tvTransactionTime)
        private val tvAmount: TextView = itemView.findViewById(R.id.tvAmount)
        private val tvTransactionStatus: TextView = itemView.findViewById(R.id.tvTransactionStatus)

        fun bind(transaction: Transaction) {
            tvMerchantName.text = transaction.merchantName
            tvTransactionTime.text = transaction.time

            // 格式化金额并设置不同颜色
            val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
            val formattedAmount = numberFormat.format(Math.abs(transaction.amount))

            // 正数为收入，负数为支出
            if (transaction.amount >= 0) {
                tvAmount.text = "+$formattedAmount"
                tvAmount.setTextColor(itemView.context.getColor(R.color.transaction_positive))
            } else {
                tvAmount.text = "-$formattedAmount"
                tvAmount.setTextColor(itemView.context.getColor(R.color.transaction_negative))
            }

            // 显示交易状态
            if (transaction.status.isNotEmpty() && transaction.status != "info") {
                tvTransactionStatus.visibility = View.VISIBLE
                when (transaction.status) {
                    "success" -> {
                        tvTransactionStatus.text = "成功"
                        tvTransactionStatus.setTextColor(itemView.context.getColor(R.color.transaction_positive))
                    }
                    "failed" -> {
                        tvTransactionStatus.text = "失败"
                        tvTransactionStatus.setTextColor(itemView.context.getColor(R.color.transaction_negative))
                    }
                    "pending" -> {
                        tvTransactionStatus.text = "处理中"
                        tvTransactionStatus.setTextColor(itemView.context.getColor(R.color.visa_blue))
                    }
                    else -> {
                        tvTransactionStatus.visibility = View.GONE
                    }
                }
            } else {
                tvTransactionStatus.visibility = View.GONE
            }
        }
    }
} 