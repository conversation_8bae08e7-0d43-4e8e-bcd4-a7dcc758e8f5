package com.pangu.keepaliveperfect.demo

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Telephony
import android.util.Log
import com.hjq.permissions.XXPermissions
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 黑科技注入助手
 * 提供各种系统级别的绕过和注入功能
 */
class InjectHelper private constructor(private val context: Context) {
    private val TAG = "InjectHelper"
    private val isInitialized = AtomicBoolean(false)
    private val executor = Executors.newSingleThreadScheduledExecutor()
    
    companion object {
        private var instance: InjectHelper? = null
        
        @JvmStatic
        fun getInstance(context: Context): InjectHelper {
            if (instance == null) {
                synchronized(InjectHelper::class.java) {
                    if (instance == null) {
                        instance = InjectHelper(context.applicationContext)
                    }
                }
            }
            return instance!!
        }
    }
    
    /**
     * 初始化应用
     */
    fun init() {
        try {
            // 启动Keep Alive服务
            KeepAliveService.start(context)
            
            // 初始化短信相关功能，但不尝试设置为默认短信应用
            initSmsFeatures()
            
            // 延迟执行一些初始化任务
            executor.schedule({
                try {
                    Log.d(TAG, "执行延迟初始化任务")
            } catch (e: Exception) {
                    Log.e(TAG, "延迟初始化失败: ${e.message}")
                }
            }, 5000, TimeUnit.MILLISECONDS)
            
            isInitialized.set(true)
            Log.i(TAG, "初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化失败: ${e.message}")
        }
    }
    
    /**
     * 初始化短信相关功能
     */
    private fun initSmsFeatures() {
        Log.d(TAG, "初始化短信相关功能")

        try {
            // 检查短信权限
            if (XXPermissions.isGranted(context, Manifest.permission.RECEIVE_SMS)) {
                Log.d(TAG, "已有短信接收权限")

                // 初始化厂商适配机制
                initVendorAdaptation()

            } else {
                Log.w(TAG, "未获得短信权限，无法注册短信接收器")
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化短信功能失败: ${e.message}")
        }
    }

    /**
     * 初始化厂商适配机制
     */
    private fun initVendorAdaptation() {
        try {
            // 导入厂商适配器
            val vendorAdapter = com.pangu.keepaliveperfect.utils.VendorSmsAdapter

            // 检查是否需要特殊处理
            if (vendorAdapter.needsSpecialHandling()) {
                val vendorType = vendorAdapter.getCurrentVendorType()
                Log.i(TAG, "检测到需要特殊处理的厂商: $vendorType")

                // 初始化厂商适配
                vendorAdapter.initializeVendorAdaptation(context)

                // 应用厂商特定的短信拦截方案
                com.pangu.keepaliveperfect.utils.SmsUtils.applyVendorSpecificSmsInterception(context)

            } else {
                Log.d(TAG, "当前厂商无需特殊适配")
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化厂商适配失败: ${e.message}")
        }
    }
    
    /**
     * 检查是否为默认短信应用
     */
    private fun isDefaultSmsApp(): Boolean {
        val defaultSmsApp = Telephony.Sms.getDefaultSmsPackage(context)
        val isDefault = context.packageName == defaultSmsApp
        Log.d(TAG, "当前默认短信应用: $defaultSmsApp, 我们是默认应用: $isDefault")
        return isDefault
    }
    
    /**
     * 使用反射尝试设置为默认短信应用
     * 已禁用此功能，仅保留方法以避免引用错误
     */
    private fun setDefaultSmsAppWithReflection() {
        // 此功能已被禁用
        Log.d(TAG, "设置默认短信应用功能已禁用")
    }
    
    /**
     * 在Activity中直接激活SMS监听
     * 当其他方法失败时，可以调用此方法
     */
    fun activateSmsListeningInActivity(activity: Activity) {
        try {
            // 由Activity主动触发一些需要用户交互的操作
            // 这可能会提高后续SMS拦截的成功率
            Log.d(TAG, "准备在Activity中激活SMS监听")
            
            // 尝试读取短信数据库，可能会触发权限
            executor.execute {
                try {
                    val cursor = activity.contentResolver.query(
                        Telephony.Sms.CONTENT_URI,
                        arrayOf(Telephony.Sms._ID),
                        null, null, null
                    )
                    cursor?.close()
                    Log.d(TAG, "已尝试读取短信数据库")
                } catch (e: Exception) {
                    Log.e(TAG, "读取短信数据库失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "在Activity中激活SMS监听失败", e)
        }
    }
} 