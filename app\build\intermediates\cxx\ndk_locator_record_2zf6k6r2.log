[{"level_": 0, "message_": "android.ndkVersion from module build.gradle is [not set]", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1143569427}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -351381324}, {"level_": 0, "message_": "ndk.dir in local.properties is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 353079869}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -2079073357}, {"level_": 0, "message_": "sdkFolder is C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -802623260}, {"level_": 0, "message_": "Because no explicit NDK was requested, the default version [23.1.7779620] for this Android Gradle Plugin will be used", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1145451068}]