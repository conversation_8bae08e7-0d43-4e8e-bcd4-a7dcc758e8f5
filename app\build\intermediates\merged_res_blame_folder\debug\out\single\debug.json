[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_customer_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_customer_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_alipay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_alipay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circular_face_detection_box.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circular_face_detection_box.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_wechat_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\wechat_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_rounded_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_rounded_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_background_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_background_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_qr_code_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\qr_code_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_american_express_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\american_express_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_system_syncadapter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\system_syncadapter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_notification_security_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\notification_security_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_status_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\status_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_rounded_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_rounded_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_sms.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_sms.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_wechat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_wechat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_privacy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_privacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_sms.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_sms.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\font_roboto_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\font\\roboto_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_amex_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_amex_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_fragment_photo_logs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\fragment_photo_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\font_roboto_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\font\\roboto_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_system_authenticator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\system_authenticator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_bank.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_bank.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_card_shine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\card_shine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_card_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_card_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_system_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_system_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_device_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_device_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_visa_4.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\visa_4.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_authenticator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\authenticator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_tools_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_tools_menu.xml"}, {"merged": "com.pangu.keepaliveperfect.demo.app-merged_res-49:/drawable_ic_uninstall.xml.flat", "source": "com.pangu.keepaliveperfect.demo.app-main-51:/drawable/ic_uninstall.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_tencent_qq.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\tencent_qq.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_bill.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_bill.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_security_shield.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_security_shield.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_identity_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_identity_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_notification_system_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\notification_system_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_wechat_qr_code.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\wechat_qr_code.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_logs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_notification_system_optimization.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\notification_system_optimization.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\font_roboto_medium.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\font\\roboto_medium.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_visa.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\visa.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_qq_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\qq_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_transfer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_transfer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_visibility_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_visibility_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_layout_visa_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\layout_visa_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_photo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_photo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_pending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_pending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_qiniu_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_qiniu_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_transfer_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_transfer_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_digital_currency.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_digital_currency.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_device_admin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\device_admin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_scale_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\scale_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_card_shine_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\card_shine_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_syncadapter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\syncadapter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_visibility_toggle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_visibility_toggle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_main_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_main_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_transfer_form.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_transfer_form.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_mastercard_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\mastercard_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\font_visa_font.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\font\\visa_font.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circular_mask.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circular_mask.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_phone_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_phone_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_background_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_background_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_visa_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_visa_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_account.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_account.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_bill_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_bill_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_notification_simple_security.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\notification_simple_security.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_notification_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\notification_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_payment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_payment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_card_highlight.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_card_highlight.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_change_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_change_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_register.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_tools.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_tools.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_bill_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_bill_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_mastercard_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_mastercard_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_app_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_app_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_visa2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\visa2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_face_detection_box.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\face_detection_box.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_card_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_card_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\color_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\color\\nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_account_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_account_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_bg_card_shadow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\bg_card_shadow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_qq.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_qq.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_fragment_sms_logs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\fragment_sms_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_background_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_background_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_wechat_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_wechat_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_error_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_error_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_transaction_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_transaction_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_rounded_button_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\rounded_button_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_weixin.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\weixin.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_device_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_device_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_logs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_slide_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\slide_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_payment_form.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_payment_form.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\mipmap-anydpi_visa.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\mipmap-anydpi\\visa.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_status_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_status_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_alipay_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\alipay_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_security.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_security.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_simple_permission.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_simple_permission.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_scale_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\scale_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_notification_icon_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\notification_icon_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_visa.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_visa.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_visibility.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_visibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\font_roboto.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\font\\roboto.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_mobile_phone.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\mobile_phone.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_item_visa_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\item_visa_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_tudou.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_tudou.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_ic_me.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\ic_me.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_activity_qq_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\activity_qq_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\drawable_circle_background_gold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\drawable\\circle_background_gold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_account_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_account_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-merged_res-49:\\layout_dialog_face_recognition.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.pangu.keepaliveperfect.demo.app-main-51:\\layout\\dialog_face_recognition.xml"}]