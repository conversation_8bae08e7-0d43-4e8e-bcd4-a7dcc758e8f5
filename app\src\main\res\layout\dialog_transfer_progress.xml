<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_dialog"
    android:padding="24dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="提现进度"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/divider_color"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <LinearLayout
        android:id="@+id/llProgressSteps"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:id="@+id/ivStep1"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/visa_blue" />

            <TextView
                android:id="@+id/tvStep1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="确认提现"
                android:textColor="@color/visa_blue"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:id="@+id/ivStep2"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_pending"
                app:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvStep2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="正在验证..."
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:id="@+id/ivStep3"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_pending"
                app:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvStep3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="提交..."
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:id="@+id/ivStep4"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_pending"
                app:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvStep4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="处理结果"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tvProgressStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="正在处理，预计剩余时间：5分钟"
        android:textColor="@color/visa_blue"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llProgressSteps" />

    <TextView
        android:id="@+id/tvResultMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text=""
        android:textColor="@color/error_red"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProgressStatus" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnClose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:backgroundTint="@color/visa_blue"
        android:text="关闭"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvResultMessage" />

</androidx.constraintlayout.widget.ConstraintLayout>
