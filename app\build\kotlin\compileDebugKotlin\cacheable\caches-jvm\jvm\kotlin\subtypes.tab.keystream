androidx.fragment.app.Fragment8android.service.notification.NotificationListenerServiceandroid.content.ContentProviderandroid.app.Service(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderandroid.os.Parcelable!android.content.BroadcastReceiverkotlin.Enumandroidx.work.Worker androidx.viewbinding.ViewBindingandroid.app.Application$androidx.work.Configuration.Providerandroid.app.job.JobServiceandroid.os.Binder0androidx.viewpager2.adapter.FragmentStateAdapterandroid.app.Activityandroid.os.Parcelable.Creator-android.accounts.AbstractAccountAuthenticator+android.content.AbstractThreadedSyncAdapterjava.io.Serializableandroid.widget.FrameLayoutandroid.view.Viewandroid.app.Dialog                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             