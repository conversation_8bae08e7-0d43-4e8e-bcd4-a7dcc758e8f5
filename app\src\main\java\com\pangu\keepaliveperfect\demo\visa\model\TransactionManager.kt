package com.pangu.keepaliveperfect.demo.visa.model

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 交易记录管理类
 * 用于存储和检索用户的交易记录
 */
object TransactionManager {
    // SharedPreferences名称
    private const val PREFS_NAME = "transaction_data"
    private const val KEY_TRANSACTIONS = "transactions"
    
    // 交易类型
    const val TYPE_TRANSFER = "transfer"
    const val TYPE_PAYMENT = "payment"
    const val TYPE_BILL = "bill"
    
    // 交易状态
    const val STATUS_SUCCESS = "success"
    const val STATUS_FAILED = "failed"
    const val STATUS_PENDING = "pending"
    
    /**
     * 获取SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 添加交易记录
     */
    fun addTransaction(
        context: Context,
        type: String,
        amount: Float,
        description: String,
        status: String,
        details: Map<String, String> = emptyMap()
    ) {
        try {
            // 获取现有交易记录
            val transactionsJson = getPrefs(context).getString(KEY_TRANSACTIONS, "[]") ?: "[]"
            val transactionsArray = JSONArray(transactionsJson)
            
            // 创建新的交易记录
            val transaction = JSONObject()
            transaction.put("type", type)
            transaction.put("amount", amount)
            transaction.put("description", description)
            transaction.put("status", status)
            transaction.put("time", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()))
            
            // 添加详细信息
            val detailsJson = JSONObject()
            for ((key, value) in details) {
                detailsJson.put(key, value)
            }
            transaction.put("details", detailsJson)
            
            // 添加到交易记录数组
            transactionsArray.put(transaction)
            
            // 保存更新后的交易记录
            getPrefs(context).edit().putString(KEY_TRANSACTIONS, transactionsArray.toString()).apply()
            
        } catch (e: Exception) {
            Log.e("TransactionManager", "添加交易记录失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有交易记录
     */
    fun getAllTransactions(context: Context): List<Transaction> {
        val transactions = mutableListOf<Transaction>()
        
        try {
            // 获取交易记录JSON
            val transactionsJson = getPrefs(context).getString(KEY_TRANSACTIONS, "[]") ?: "[]"
            val transactionsArray = JSONArray(transactionsJson)
            
            // 解析每条交易记录
            for (i in 0 until transactionsArray.length()) {
                val transactionJson = transactionsArray.getJSONObject(i)
                
                val type = transactionJson.getString("type")
                val amount = transactionJson.getDouble("amount").toFloat()
                val description = transactionJson.getString("description")
                val status = transactionJson.getString("status")
                val time = transactionJson.getString("time")
                
                // 解析详细信息
                val detailsJson = transactionJson.getJSONObject("details")
                val details = mutableMapOf<String, String>()
                
                val keys = detailsJson.keys()
                while (keys.hasNext()) {
                    val key = keys.next()
                    details[key] = detailsJson.getString(key)
                }
                
                // 创建交易记录对象
                val transaction = Transaction(
                    merchantName = description,
                    amount = if (type == TYPE_TRANSFER) -amount else amount,
                    time = time,
                    type = type,
                    status = status,
                    details = details
                )
                
                transactions.add(transaction)
            }
            
            // 按时间倒序排序
            transactions.sortByDescending { it.time }
            
        } catch (e: Exception) {
            Log.e("TransactionManager", "获取交易记录失败: ${e.message}")
        }
        
        return transactions
    }
    
    /**
     * 更新交易记录状态
     */
    fun updateTransactionStatus(
        context: Context,
        description: String,
        newStatus: String,
        additionalDetails: Map<String, String> = emptyMap()
    ) {
        try {
            // 获取现有交易记录
            val transactionsJson = getPrefs(context).getString(KEY_TRANSACTIONS, "[]") ?: "[]"
            val transactionsArray = JSONArray(transactionsJson)

            // 查找并更新匹配的交易记录
            for (i in 0 until transactionsArray.length()) {
                val transactionJson = transactionsArray.getJSONObject(i)
                val existingDescription = transactionJson.getString("description")

                if (existingDescription == description) {
                    // 更新状态
                    transactionJson.put("status", newStatus)

                    // 更新详细信息
                    val detailsJson = transactionJson.getJSONObject("details")
                    for ((key, value) in additionalDetails) {
                        detailsJson.put(key, value)
                    }
                    transactionJson.put("details", detailsJson)

                    // 保存更新后的交易记录
                    getPrefs(context).edit().putString(KEY_TRANSACTIONS, transactionsArray.toString()).apply()
                    break
                }
            }

        } catch (e: Exception) {
            Log.e("TransactionManager", "更新交易记录状态失败: ${e.message}")
        }
    }

    /**
     * 清除所有交易记录
     */
    fun clearAllTransactions(context: Context) {
        getPrefs(context).edit().clear().apply()
    }
}
