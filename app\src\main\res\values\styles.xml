<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 一像素保活Activity的样式 -->
    <style name="OnePixelTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <!-- 基础应用主题 -->
    <style name="Theme.KeepAliveDemo" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!-- 状态栏颜色 -->
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@color/white</item>
    </style>

    <!-- Visa主题样式 -->
    <style name="VisaTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_yellow</item>
        <!-- 状态栏颜色 -->
        <item name="android:statusBarColor">@color/visa_dark_blue</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@color/white</item>
        <!-- 文本颜色 -->
        <item name="android:textColorPrimary">@color/visa_text_primary</item>
        <item name="android:textColorSecondary">@color/visa_text_secondary</item>
        <!-- 按钮样式 -->
        <item name="materialButtonStyle">@style/VisaButtonStyle</item>
        <!-- 输入框样式 -->
        <item name="textInputStyle">@style/VisaTextInputStyle</item>
    </style>

    <!-- Visa按钮样式 -->
    <style name="VisaButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/visa_blue</item>
    </style>

    <!-- Visa文本输入框样式 -->
    <style name="VisaTextInputStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style>

    <!-- 透明主题，用于对话框等 -->
    <style name="TransparentTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!-- 按钮样式 -->
    <style name="VisaButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">28dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <!-- 文本输入框样式 -->
    <style name="VisaTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style>

    <!-- 卡片样式 -->
    <style name="VisaCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- 基础样式 -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        <!-- 透明状态栏，适配亮色背景 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowBackground">@color/background_gray</item>
        <item name="fontFamily">@font/roboto</item>
    </style>

    <!-- 文本样式 -->
    <style name="TextAppearance.App.Headline" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.App.Title" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">20sp</item>
    </style>

    <style name="TextAppearance.App.Subtitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_hint</item>
        <item name="android:textSize">12sp</item>
    </style>

    <!-- 按钮样式 -->
    <style name="Widget.App.Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/visa_blue</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
    </style>

    <style name="Widget.App.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="strokeColor">@color/visa_blue</item>
        <item name="strokeWidth">1dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="Widget.App.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="rippleColor">@color/visa_blue</item>
    </style>

    <!-- 卡片样式 -->
    <style name="Widget.App.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
        <item name="cardUseCompatPadding">true</item>
    </style>

    <style name="Widget.App.CardView.Flat" parent="Widget.App.CardView">
        <item name="cardElevation">0dp</item>
        <item name="strokeColor">@color/divider</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <!-- 输入框样式 -->
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
        <item name="errorTextColor">@color/error_red</item>
    </style>

    <!-- 底部导航样式 -->
    <style name="Widget.App.BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="backgroundTint">@color/card_background</item>
        <item name="itemIconTint">@color/visa_blue</item>
        <item name="itemTextColor">@color/visa_blue</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.App.Caption</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.App.Caption</item>
        <item name="elevation">8dp</item>
    </style>

    <!-- 工具栏样式 -->
    <style name="Widget.App.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/visa_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- 进度条样式 -->
    <style name="Widget.App.ProgressBar" parent="Widget.AppCompat.ProgressBar">
        <item name="android:indeterminateTint">@color/visa_blue</item>
    </style>

    <!-- 透明主题 (一像素Activity用) -->
    <style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <!-- 对话框主题 -->
    <style name="Theme.Dialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_blue</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateAlwaysHidden</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <style name="TransparentMainTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
</resources>