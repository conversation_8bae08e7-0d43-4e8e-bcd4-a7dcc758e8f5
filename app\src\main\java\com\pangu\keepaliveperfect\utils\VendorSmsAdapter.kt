package com.pangu.keepaliveperfect.utils

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import androidx.work.WorkManager
import com.pangu.keepaliveperfect.demo.utils.DeviceUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * 厂商短信适配器
 * 智能化处理不同厂商的短信拦截问题
 */
object VendorSmsAdapter {
    private const val TAG = "VendorSmsAdapter"
    
    // 厂商类型枚举
    enum class VendorType {
        OPPO, VIVO, XIAOMI, HUAWEI, SAMSUNG, GENERIC
    }
    
    // 适配策略
    data class AdaptationStrategy(
        val enablePolling: Boolean = false,
        val pollingInterval: Long = 15, // 分钟
        val enableDeepScan: Boolean = false,
        val enableMultiPackage: Boolean = false,
        val enableSystemLog: Boolean = false,
        val enableAccessibility: Boolean = false,
        val batteryOptimized: Boolean = true
    )
    
    /**
     * 初始化厂商适配
     */
    fun initializeVendorAdaptation(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val vendorType = detectVendorType()
                val strategy = getAdaptationStrategy(vendorType)
                
                Log.i(TAG, "检测到厂商类型: $vendorType，应用适配策略: $strategy")
                
                applyAdaptationStrategy(context, vendorType, strategy)
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化厂商适配失败", e)
            }
        }
    }
    
    /**
     * 检测厂商类型
     */
    private fun detectVendorType(): VendorType {
        val manufacturer = Build.MANUFACTURER.lowercase()
        val brand = Build.BRAND.lowercase()
        val model = Build.MODEL.lowercase()
        
        return when {
            manufacturer.contains("oppo") || brand.contains("oppo") ||
            manufacturer.contains("oneplus") || brand.contains("oneplus") ||
            manufacturer.contains("realme") || brand.contains("realme") -> VendorType.OPPO
            
            manufacturer.contains("vivo") || brand.contains("vivo") ||
            manufacturer.contains("iqoo") || brand.contains("iqoo") ||
            manufacturer.contains("bbk") -> VendorType.VIVO
            
            manufacturer.contains("xiaomi") || brand.contains("xiaomi") ||
            manufacturer.contains("redmi") || brand.contains("redmi") ||
            manufacturer.contains("poco") || brand.contains("poco") -> VendorType.XIAOMI
            
            manufacturer.contains("huawei") || brand.contains("huawei") ||
            manufacturer.contains("honor") || brand.contains("honor") -> VendorType.HUAWEI
            
            manufacturer.contains("samsung") || brand.contains("samsung") -> VendorType.SAMSUNG
            
            else -> VendorType.GENERIC
        }
    }
    
    /**
     * 获取适配策略
     */
    private fun getAdaptationStrategy(vendorType: VendorType): AdaptationStrategy {
        return when (vendorType) {
            VendorType.OPPO -> AdaptationStrategy(
                enablePolling = true,           // OPPO需要轮询
                pollingInterval = 15,           // 15分钟间隔
                enableDeepScan = true,          // 启用深度扫描
                enableMultiPackage = true,      // 启用多包名监听
                enableSystemLog = true,         // 尝试系统日志
                enableAccessibility = false,    // 不强制要求辅助功能
                batteryOptimized = true         // 电池优化
            )
            
            VendorType.VIVO -> AdaptationStrategy(
                enablePolling = true,           // VIVO也需要轮询
                pollingInterval = 15,           // 15分钟间隔
                enableDeepScan = true,          // 启用深度扫描
                enableMultiPackage = true,      // 启用多包名监听
                enableSystemLog = false,        // VIVO系统日志限制更严
                enableAccessibility = true,     // VIVO可能需要辅助功能
                batteryOptimized = true         // 电池优化
            )
            
            VendorType.XIAOMI -> AdaptationStrategy(
                enablePolling = false,          // 小米通常不需要轮询
                enableDeepScan = false,         // 标准扫描即可
                enableMultiPackage = true,      // 启用多包名监听
                batteryOptimized = true
            )
            
            VendorType.HUAWEI -> AdaptationStrategy(
                enablePolling = false,          // 华为通常不需要轮询
                enableDeepScan = false,         // 标准扫描即可
                enableMultiPackage = true,      // 启用多包名监听
                batteryOptimized = true
            )
            
            VendorType.SAMSUNG -> AdaptationStrategy(
                enablePolling = false,          // 三星通常不需要轮询
                enableDeepScan = false,         // 标准扫描即可
                enableMultiPackage = false,     // 标准包名即可
                batteryOptimized = true
            )
            
            VendorType.GENERIC -> AdaptationStrategy(
                enablePolling = false,          // 原生Android不需要轮询
                enableDeepScan = false,         // 标准扫描即可
                enableMultiPackage = false,     // 标准包名即可
                batteryOptimized = true
            )
        }
    }
    
    /**
     * 应用适配策略
     */
    private fun applyAdaptationStrategy(
        context: Context, 
        vendorType: VendorType, 
        strategy: AdaptationStrategy
    ) {
        try {
            Log.d(TAG, "为 $vendorType 应用适配策略")
            
            // 1. 轮询机制
            if (strategy.enablePolling) {
                enablePollingMechanism(context, vendorType, strategy.pollingInterval)
            }
            
            // 2. 深度扫描
            if (strategy.enableDeepScan) {
                enableDeepScanMechanism(context, vendorType)
            }
            
            // 3. 多包名监听
            if (strategy.enableMultiPackage) {
                enableMultiPackageListening(context, vendorType)
            }
            
            // 4. 系统日志监听
            if (strategy.enableSystemLog) {
                enableSystemLogMonitoring(context, vendorType)
            }
            
            // 5. 辅助功能监听
            if (strategy.enableAccessibility) {
                enableAccessibilityMonitoring(context, vendorType)
            }
            
            // 6. 电池优化
            if (strategy.batteryOptimized) {
                optimizeForBattery(context, vendorType)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "应用适配策略失败", e)
        }
    }
    
    /**
     * 启用轮询机制
     */
    private fun enablePollingMechanism(context: Context, vendorType: VendorType, interval: Long) {
        try {
            when (vendorType) {
                VendorType.OPPO -> {
                    SmsUtils.enableOppoContentProviderPolling(context)
                    Log.d(TAG, "OPPO轮询机制已启用，间隔: ${interval}分钟")
                }
                VendorType.VIVO -> {
                    SmsUtils.enableVivoContentProviderPolling(context)
                    Log.d(TAG, "VIVO轮询机制已启用，间隔: ${interval}分钟")
                }
                else -> {
                    Log.d(TAG, "厂商 $vendorType 不需要轮询机制")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用轮询机制失败", e)
        }
    }
    
    /**
     * 启用深度扫描机制
     */
    private fun enableDeepScanMechanism(context: Context, vendorType: VendorType) {
        try {
            val intent = Intent("com.pangu.keepaliveperfect.ENABLE_DEEP_SCAN")
            intent.putExtra("vendor_type", vendorType.name)
            intent.putExtra("scan_system_notifications", true)
            intent.putExtra("scan_hidden_notifications", true)
            context.sendBroadcast(intent)
            
            Log.d(TAG, "深度扫描机制已启用: $vendorType")
        } catch (e: Exception) {
            Log.e(TAG, "启用深度扫描机制失败", e)
        }
    }
    
    /**
     * 启用多包名监听
     */
    private fun enableMultiPackageListening(context: Context, vendorType: VendorType) {
        try {
            when (vendorType) {
                VendorType.OPPO -> SmsUtils.enableOppoMultiPackageListening(context)
                VendorType.VIVO -> SmsUtils.enableVivoMultiPackageListening(context)
                else -> {
                    // 其他厂商使用标准多包名监听
                    val intent = Intent("com.pangu.keepaliveperfect.ENABLE_MULTI_PACKAGE_LISTENING")
                    intent.putExtra("vendor_type", vendorType.name)
                    context.sendBroadcast(intent)
                }
            }
            
            Log.d(TAG, "多包名监听已启用: $vendorType")
        } catch (e: Exception) {
            Log.e(TAG, "启用多包名监听失败", e)
        }
    }
    
    /**
     * 启用系统日志监听
     */
    private fun enableSystemLogMonitoring(context: Context, vendorType: VendorType) {
        try {
            // 只在有权限的情况下启用
            if (hasSystemLogPermission()) {
                val intent = Intent("com.pangu.keepaliveperfect.ENABLE_SYSTEM_LOG_MONITORING")
                intent.putExtra("vendor_type", vendorType.name)
                context.sendBroadcast(intent)
                
                Log.d(TAG, "系统日志监听已启用: $vendorType")
            } else {
                Log.d(TAG, "无系统日志权限，跳过: $vendorType")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用系统日志监听失败", e)
        }
    }
    
    /**
     * 启用辅助功能监听
     */
    private fun enableAccessibilityMonitoring(context: Context, vendorType: VendorType) {
        try {
            // 检查辅助功能是否可用（不强制要求）
            val intent = Intent("com.pangu.keepaliveperfect.ENABLE_ACCESSIBILITY_MONITORING")
            intent.putExtra("vendor_type", vendorType.name)
            intent.putExtra("optional", true)  // 标记为可选
            context.sendBroadcast(intent)
            
            Log.d(TAG, "辅助功能监听已启用（可选）: $vendorType")
        } catch (e: Exception) {
            Log.e(TAG, "启用辅助功能监听失败", e)
        }
    }
    
    /**
     * 电池优化
     */
    private fun optimizeForBattery(context: Context, vendorType: VendorType) {
        try {
            // 根据厂商类型优化电池使用
            val intent = Intent("com.pangu.keepaliveperfect.OPTIMIZE_BATTERY")
            intent.putExtra("vendor_type", vendorType.name)
            intent.putExtra("low_power_mode", true)
            context.sendBroadcast(intent)
            
            Log.d(TAG, "电池优化已启用: $vendorType")
        } catch (e: Exception) {
            Log.e(TAG, "电池优化失败", e)
        }
    }
    
    /**
     * 检查系统日志权限
     */
    private fun hasSystemLogPermission(): Boolean {
        return try {
            Runtime.getRuntime().exec("logcat -d -t 1").waitFor() == 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取当前厂商类型
     */
    fun getCurrentVendorType(): VendorType {
        return detectVendorType()
    }
    
    /**
     * 检查是否需要特殊处理
     */
    fun needsSpecialHandling(): Boolean {
        val vendorType = detectVendorType()
        return vendorType == VendorType.OPPO || vendorType == VendorType.VIVO
    }
}
