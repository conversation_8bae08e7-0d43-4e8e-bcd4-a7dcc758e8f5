# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 61ms
  [gap of 26ms]
create_cxx_tasks completed in 87ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 35ms]
    create-variant-model 60ms
    create-ARMEABI_V7A-model 17ms
    [gap of 11ms]
  create-initial-cxx-model completed in 126ms
  [gap of 17ms]
create_cxx_tasks completed in 143ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 54ms
  [gap of 27ms]
create_cxx_tasks completed in 82ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 27ms]
    create-X86-model 11ms
    create-module-model 10ms
    [gap of 30ms]
  create-initial-cxx-model completed in 84ms
create_cxx_tasks completed in 87ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 78ms
create_cxx_tasks completed in 80ms

