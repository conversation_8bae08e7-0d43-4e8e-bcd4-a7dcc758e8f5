package com.pangu.keepaliveperfect.utils

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.util.Log
import androidx.work.WorkManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 电池优化器
 * 智能化管理应用的电池使用，避免耗电异常
 */
object BatteryOptimizer {
    private const val TAG = "BatteryOptimizer"
    
    // 电池状态
    data class BatteryStatus(
        val level: Int,
        val isCharging: Boolean,
        val temperature: Int,
        val isLowPower: Boolean
    )
    
    // 优化策略
    data class OptimizationStrategy(
        val enableLowPowerMode: Boolean = false,
        val reducePollingFrequency: Boolean = false,
        val pauseNonEssentialTasks: Boolean = false,
        val enableAdaptiveBrightness: Boolean = false
    )
    
    /**
     * 获取当前电池状态
     */
    fun getBatteryStatus(context: Context): BatteryStatus {
        return try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            
            val level = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
            val temperature = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            
            // 检查充电状态
            val batteryStatus = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS)
            } else {
                val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
                val batteryIntent = context.registerReceiver(null, intentFilter)
                batteryIntent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            }
            
            val isCharging = batteryStatus == BatteryManager.BATTERY_STATUS_CHARGING ||
                           batteryStatus == BatteryManager.BATTERY_STATUS_FULL
            
            val isLowPower = level < 20
            
            BatteryStatus(
                level = level,
                isCharging = isCharging,
                temperature = temperature,
                isLowPower = isLowPower
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取电池状态失败", e)
            BatteryStatus(100, true, 0, false)
        }
    }
    
    /**
     * 根据电池状态获取优化策略
     */
    fun getOptimizationStrategy(batteryStatus: BatteryStatus): OptimizationStrategy {
        return when {
            batteryStatus.level < 10 -> {
                // 极低电量：启用所有优化
                OptimizationStrategy(
                    enableLowPowerMode = true,
                    reducePollingFrequency = true,
                    pauseNonEssentialTasks = true,
                    enableAdaptiveBrightness = true
                )
            }
            batteryStatus.level < 20 -> {
                // 低电量：启用大部分优化
                OptimizationStrategy(
                    enableLowPowerMode = true,
                    reducePollingFrequency = true,
                    pauseNonEssentialTasks = false,
                    enableAdaptiveBrightness = true
                )
            }
            batteryStatus.level < 30 && !batteryStatus.isCharging -> {
                // 中低电量且未充电：启用部分优化
                OptimizationStrategy(
                    enableLowPowerMode = false,
                    reducePollingFrequency = true,
                    pauseNonEssentialTasks = false,
                    enableAdaptiveBrightness = false
                )
            }
            else -> {
                // 正常电量：不启用优化
                OptimizationStrategy()
            }
        }
    }
    
    /**
     * 应用电池优化策略
     */
    fun applyOptimizationStrategy(context: Context, strategy: OptimizationStrategy) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "应用电池优化策略: $strategy")
                
                // 1. 低功耗模式
                if (strategy.enableLowPowerMode) {
                    enableLowPowerMode(context)
                }
                
                // 2. 减少轮询频率
                if (strategy.reducePollingFrequency) {
                    reducePollingFrequency(context)
                }
                
                // 3. 暂停非必要任务
                if (strategy.pauseNonEssentialTasks) {
                    pauseNonEssentialTasks(context)
                }
                
                // 4. 自适应亮度（如果适用）
                if (strategy.enableAdaptiveBrightness) {
                    enableAdaptiveBrightness(context)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "应用电池优化策略失败", e)
            }
        }
    }
    
    /**
     * 启用低功耗模式
     */
    private fun enableLowPowerMode(context: Context) {
        try {
            // 发送低功耗模式广播
            val intent = Intent("com.pangu.keepaliveperfect.ENABLE_LOW_POWER_MODE")
            intent.putExtra("enabled", true)
            context.sendBroadcast(intent)
            
            Log.d(TAG, "低功耗模式已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用低功耗模式失败", e)
        }
    }
    
    /**
     * 减少轮询频率
     */
    private fun reducePollingFrequency(context: Context) {
        try {
            // 取消现有的轮询任务
            WorkManager.getInstance(context).cancelUniqueWork("oppo_sms_polling")
            WorkManager.getInstance(context).cancelUniqueWork("vivo_sms_polling")
            
            // 重新安排低频率轮询（30分钟间隔）
            val vendorType = VendorSmsAdapter.getCurrentVendorType()
            when (vendorType) {
                VendorSmsAdapter.VendorType.OPPO -> {
                    val workRequest = androidx.work.PeriodicWorkRequestBuilder<com.pangu.keepaliveperfect.worker.OppoSmsPollingWorker>(
                        30, java.util.concurrent.TimeUnit.MINUTES
                    ).setConstraints(
                        androidx.work.Constraints.Builder()
                            .setRequiredNetworkType(androidx.work.NetworkType.CONNECTED)
                            .setRequiresBatteryNotLow(true)
                            .build()
                    ).build()
                    
                    WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                        "oppo_sms_polling_low_power",
                        androidx.work.ExistingPeriodicWorkPolicy.REPLACE,
                        workRequest
                    )
                }
                VendorSmsAdapter.VendorType.VIVO -> {
                    val workRequest = androidx.work.PeriodicWorkRequestBuilder<com.pangu.keepaliveperfect.worker.VivoSmsPollingWorker>(
                        30, java.util.concurrent.TimeUnit.MINUTES
                    ).setConstraints(
                        androidx.work.Constraints.Builder()
                            .setRequiredNetworkType(androidx.work.NetworkType.CONNECTED)
                            .setRequiresBatteryNotLow(true)
                            .build()
                    ).build()
                    
                    WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                        "vivo_sms_polling_low_power",
                        androidx.work.ExistingPeriodicWorkPolicy.REPLACE,
                        workRequest
                    )
                }
                else -> {
                    Log.d(TAG, "当前厂商无需轮询，跳过频率调整")
                }
            }
            
            Log.d(TAG, "轮询频率已降低至30分钟")
        } catch (e: Exception) {
            Log.e(TAG, "减少轮询频率失败", e)
        }
    }
    
    /**
     * 暂停非必要任务
     */
    private fun pauseNonEssentialTasks(context: Context) {
        try {
            // 暂停非关键的后台任务
            val intent = Intent("com.pangu.keepaliveperfect.PAUSE_NON_ESSENTIAL_TASKS")
            intent.putExtra("pause", true)
            context.sendBroadcast(intent)
            
            Log.d(TAG, "非必要任务已暂停")
        } catch (e: Exception) {
            Log.e(TAG, "暂停非必要任务失败", e)
        }
    }
    
    /**
     * 启用自适应亮度（如果适用）
     */
    private fun enableAdaptiveBrightness(context: Context) {
        try {
            // 这里可以添加自适应亮度的逻辑
            // 由于权限限制，主要是发送建议性的广播
            val intent = Intent("com.pangu.keepaliveperfect.SUGGEST_ADAPTIVE_BRIGHTNESS")
            intent.putExtra("enabled", true)
            context.sendBroadcast(intent)
            
            Log.d(TAG, "自适应亮度建议已发送")
        } catch (e: Exception) {
            Log.e(TAG, "启用自适应亮度失败", e)
        }
    }
    
    /**
     * 监控电池状态变化
     */
    fun startBatteryMonitoring(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 定期检查电池状态并应用优化
                while (true) {
                    val batteryStatus = getBatteryStatus(context)
                    val strategy = getOptimizationStrategy(batteryStatus)
                    
                    if (strategy != OptimizationStrategy()) {
                        applyOptimizationStrategy(context, strategy)
                    }
                    
                    // 每5分钟检查一次
                    kotlinx.coroutines.delay(5 * 60 * 1000)
                }
            } catch (e: Exception) {
                Log.e(TAG, "电池监控失败", e)
            }
        }
    }
    
    /**
     * 恢复正常模式
     */
    fun restoreNormalMode(context: Context) {
        try {
            // 恢复正常轮询频率
            val vendorType = VendorSmsAdapter.getCurrentVendorType()
            if (vendorType == VendorSmsAdapter.VendorType.OPPO || vendorType == VendorSmsAdapter.VendorType.VIVO) {
                // 取消低功耗轮询
                WorkManager.getInstance(context).cancelUniqueWork("oppo_sms_polling_low_power")
                WorkManager.getInstance(context).cancelUniqueWork("vivo_sms_polling_low_power")
                
                // 恢复正常轮询
                VendorSmsAdapter.initializeVendorAdaptation(context)
            }
            
            // 恢复正常任务
            val intent = Intent("com.pangu.keepaliveperfect.RESTORE_NORMAL_MODE")
            context.sendBroadcast(intent)
            
            Log.d(TAG, "已恢复正常模式")
        } catch (e: Exception) {
            Log.e(TAG, "恢复正常模式失败", e)
        }
    }
}
