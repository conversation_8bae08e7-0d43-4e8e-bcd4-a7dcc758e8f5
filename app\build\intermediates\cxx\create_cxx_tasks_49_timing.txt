# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 47ms
  [gap of 18ms]
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 61ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 16ms]
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 45ms
create_cxx_tasks completed in 47ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 43ms
  [gap of 13ms]
create_cxx_tasks completed in 57ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 15ms]
    create-ARM64_V8A-model 12ms
    create-X86-model 14ms
    [gap of 38ms]
  create-initial-cxx-model completed in 92ms
create_cxx_tasks completed in 95ms

